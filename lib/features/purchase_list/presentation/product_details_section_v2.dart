import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class ProductDetailsSectionV2 extends StatefulWidget {
  const ProductDetailsSectionV2({
    super.key,
    required this.selectedProductIds,
    required this.productDetails,
    required this.onProductDetailsChanged,
  });

  final List<String> selectedProductIds;
  final List<ProductDetail> productDetails;
  final ValueChanged<List<ProductDetail>> onProductDetailsChanged;

  @override
  State<ProductDetailsSectionV2> createState() =>
      _ProductDetailsSectionV2State();
}

class _ProductDetailsSectionV2State extends State<ProductDetailsSectionV2> {
  late List<ProductDetail> _productDetails;
  List<Warehouse> _warehouses = [];
  bool _warehousesLoading = false;

  @override
  void initState() {
    super.initState();
    // Отложенная инициализация после завершения построения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProductDetails();
      _loadWarehouses();
    });
  }

  @override
  void didUpdateWidget(ProductDetailsSectionV2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedProductIds != widget.selectedProductIds) {
      // Отложенная инициализация после завершения построения
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeProductDetails();
      });
    }
  }

  void _initializeProductDetails() {
    _productDetails = List.from(widget.productDetails);

    bool hasChanges = false;

    // Создаем записи для всех выбранных продуктов, если их еще нет
    for (final productId in widget.selectedProductIds) {
      if (!_productDetails.any((detail) => detail.productId == productId)) {
        _productDetails.add(ProductDetail(
          productId: productId,
          price: 0.0,
          unit: 'шт',
          distributions: [
            Distribution(
              warehouseId: null,
              quantity: 1.0,
              deliveryDate: null,
              notes: null,
            ),
          ],
          notes: null,
        ));
        hasChanges = true;
      }
    }

    // Удаляем записи для продуктов, которые больше не выбраны
    final initialLength = _productDetails.length;
    _productDetails.removeWhere(
      (detail) => !widget.selectedProductIds.contains(detail.productId),
    );
    if (_productDetails.length != initialLength) {
      hasChanges = true;
    }

    // Отложенное уведомление об изменениях после завершения построения
    if (hasChanges) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _notifyChange();
      });
    }
  }

  void _notifyChange() {
    if (mounted) {
      widget.onProductDetailsChanged(_productDetails);
    }
  }

  void _updateProductDetail(int index, ProductDetail updatedDetail) {
    if (mounted && index >= 0 && index < _productDetails.length) {
      setState(() {
        _productDetails[index] = updatedDetail;
      });
      _notifyChange();
    }
  }

  Future<void> _loadWarehouses() async {
    if (!mounted) return;

    setState(() {
      _warehousesLoading = true;
    });

    try {
      final response = await PurchaseListRepositoryV2.searchWarehouses();
      if (mounted && response.data != null) {
        setState(() {
          // Парсим ответ API
          if (response.data is Map<String, dynamic> &&
              response.data['data'] is List) {
            final List<dynamic> warehousesList = response.data['data'];
            _warehouses = warehousesList
                .map((item) => Warehouse.fromJson(item as Map<String, dynamic>))
                .toList();
          } else if (response.data is List) {
            _warehouses = (response.data as List)
                .map((item) => Warehouse.fromJson(item as Map<String, dynamic>))
                .toList();
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка загрузки складов: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _warehousesLoading = false;
        });
      }
    }
  }

  Future<void> _pickDate(
      BuildContext context, int productIndex, int distributionIndex) async {
    if (!mounted ||
        productIndex < 0 ||
        productIndex >= _productDetails.length) {
      return;
    }

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null && mounted) {
      final formattedDate =
          '${pickedDate.year}-${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.day.toString().padLeft(2, '0')}';

      final updatedDistributions =
          List<Distribution>.from(productDetail.distributions!);
      updatedDistributions[distributionIndex] =
          updatedDistributions[distributionIndex]
              .copyWith(deliveryDate: formattedDate);

      _updateProductDetail(
        productIndex,
        productDetail.copyWith(distributions: updatedDistributions),
      );
    }
  }

  void _addDistribution(int productIndex) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    final currentDistributions = productDetail.distributions ?? [];

    final updatedDistributions = List<Distribution>.from(currentDistributions)
      ..add(Distribution(
        warehouseId: null,
        quantity: 1.0,
        deliveryDate: null,
        notes: null,
      ));

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }

  void _removeDistribution(int productIndex, int distributionIndex) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    // Не позволяем удалить последнее распределение
    if (productDetail.distributions!.length <= 1) {
      return;
    }

    final updatedDistributions =
        List<Distribution>.from(productDetail.distributions!)
          ..removeAt(distributionIndex);

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_productDetails.isEmpty) {
      return const SizedBox.shrink();
    }

    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Детали продуктов',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16.0),
            if (_warehousesLoading)
              const Center(child: CircularProgressIndicator())
            else
              ..._productDetails.asMap().entries.map((entry) {
                final index = entry.key;
                final detail = entry.value;
                return _buildProductDetailCard(index, detail);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildProductDetailCard(int productIndex, ProductDetail detail) {
    return CustomCard(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Заголовок продукта
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Продукт: ${detail.productId}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16.0),

            // Основная информация о продукте
            _buildProductBasicInfo(productIndex, detail),

            const SizedBox(height: 16.0),

            // Распределения
            _buildDistributionsSection(productIndex, detail),
          ],
        ),
      ),
    );
  }

  Widget _buildProductBasicInfo(int productIndex, ProductDetail detail) {
    return Column(
      children: [
        // Первая строка: Цена и Единица измерения
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: detail.price?.toString() ?? '',
                decoration: const InputDecoration(
                  labelText: 'Цена *',
                  border: OutlineInputBorder(),
                  suffixText: '₽',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  _updateProductDetail(
                    productIndex,
                    detail.copyWith(price: price),
                  );
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Цена обязательна';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price < 0) {
                    return 'Цена должна быть >= 0';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: TextFormField(
                initialValue: detail.unit ?? '',
                decoration: const InputDecoration(
                  labelText: 'Единица измерения',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  _updateProductDetail(
                    productIndex,
                    detail.copyWith(unit: value.isEmpty ? null : value),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),

        // Примечания к продукту
        TextFormField(
          initialValue: detail.notes ?? '',
          decoration: const InputDecoration(
            labelText: 'Примечания к продукту',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          onChanged: (value) {
            _updateProductDetail(
              productIndex,
              detail.copyWith(notes: value.isEmpty ? null : value),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDistributionsSection(int productIndex, ProductDetail detail) {
    final distributions = detail.distributions ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Распределения по складам',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const Spacer(),
            IconButton(
              onPressed: () => _addDistribution(productIndex),
              icon: const Icon(Icons.add),
              tooltip: 'Добавить распределение',
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        if (distributions.isEmpty)
          const Text('Нет распределений')
        else
          ...distributions.asMap().entries.map((entry) {
            final distributionIndex = entry.key;
            final distribution = entry.value;
            return _buildDistributionCard(
                productIndex, distributionIndex, distribution);
          }),
      ],
    );
  }

  Widget _buildDistributionCard(
      int productIndex, int distributionIndex, Distribution distribution) {
    return CustomCard(
      color: Colors.grey[50],
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            // Заголовок распределения
            Row(
              children: [
                Text(
                  'Распределение ${distributionIndex + 1}',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                if (_productDetails[productIndex].distributions!.length > 1)
                  IconButton(
                    onPressed: () =>
                        _removeDistribution(productIndex, distributionIndex),
                    icon: const Icon(Icons.delete, size: 20),
                    tooltip: 'Удалить распределение',
                  ),
              ],
            ),
            const SizedBox(height: 12.0),

            _buildDistributionFields(
                productIndex, distributionIndex, distribution),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionFields(
      int productIndex, int distributionIndex, Distribution distribution) {
    return Column(
      children: [
        // Первая строка: Склад и Количество
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: distribution.warehouseId,
                decoration: const InputDecoration(
                  labelText: 'Склад',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('Не выбран'),
                  ),
                  ..._warehouses.map((warehouse) => DropdownMenuItem<String>(
                        value: warehouse.id,
                        child: Text(warehouse.name),
                      )),
                ],
                onChanged: (value) {
                  _updateDistribution(productIndex, distributionIndex,
                      distribution.copyWith(warehouseId: value));
                },
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: TextFormField(
                initialValue: distribution.quantity?.toString() ?? '',
                decoration: const InputDecoration(
                  labelText: 'Количество *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  final quantity = double.tryParse(value) ?? 1.0;
                  _updateDistribution(productIndex, distributionIndex,
                      distribution.copyWith(quantity: quantity));
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Количество обязательно';
                  }
                  final quantity = double.tryParse(value);
                  if (quantity == null || quantity <= 0) {
                    return 'Количество должно быть > 0';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),

        // Вторая строка: Дата поставки
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: TextEditingController(
                    text: distribution.deliveryDate ?? ''),
                decoration: InputDecoration(
                  labelText: 'Дата поставки',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.calendar_today),
                    onPressed: () =>
                        _pickDate(context, productIndex, distributionIndex),
                  ),
                ),
                readOnly: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),

        // Примечания к распределению
        TextFormField(
          initialValue: distribution.notes ?? '',
          decoration: const InputDecoration(
            labelText: 'Примечания к распределению',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          onChanged: (value) {
            _updateDistribution(productIndex, distributionIndex,
                distribution.copyWith(notes: value.isEmpty ? null : value));
          },
        ),
      ],
    );
  }

  void _updateDistribution(int productIndex, int distributionIndex,
      Distribution updatedDistribution) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    final updatedDistributions =
        List<Distribution>.from(productDetail.distributions!);
    updatedDistributions[distributionIndex] = updatedDistribution;

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }
}
