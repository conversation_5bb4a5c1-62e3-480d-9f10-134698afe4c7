import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/debouncer.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/warehouses/presentation/bloc/bloc.dart';
import 'package:sphere/features/warehouses/presentation/create_warehouse.dart';
import 'package:sphere/features/warehouses/presentation/edit_warehouse.dart';
import 'package:sphere/features/warehouses/presentation/warehouse_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class WarehousesScreen extends StatefulWidget {
  const WarehousesScreen({super.key});

  @override
  State<WarehousesScreen> createState() => _WarehousesScreenState();
}

class _WarehousesScreenState extends State<WarehousesScreen>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(latency: 500);

  late BlocWarehouses _warehousesBloc;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _warehousesBloc = BlocWarehouses();
    _warehousesBloc.add(const BlocWarehousesEvents.loadWarehouses());
    _setAppBarConfig();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _warehousesBloc.close();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    _debouncer.run(() {
      _warehousesBloc.add(BlocWarehousesEvents.searchWarehouses(value));
    });
  }

  void _setAppBarConfig() {
    final newConfig = CustomAppBarConfig(
      title: 'Склады',
      description: 'Управление складами и их содержимым',
      rightPart: CustomAppBarFeatures.getPopupMenu(
        context: context,
        children: [
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              showBaseDialog(
                context,
                maxWidth: 500,
                builder: (context) => CreateWarehousePopup(
                  onSuccess: () {
                    _warehousesBloc
                        .add(const BlocWarehousesEvents.loadWarehouses());
                  },
                ),
              );
            },
            icon: Assets.icons.add,
            text: 'Создать склад',
            description: 'Добавить новый склад',
          ),
          CustomDropdownMenuItem(
            onTap: () {
              CustomDropdownMenu.instance.hide();
              _warehousesBloc.add(const BlocWarehousesEvents.loadWarehouses());
            },
            icon: Assets.icons.repeat,
            text: 'Обновить',
            description: 'Обновить список складов',
          ),
        ],
      ),
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return BlocProvider.value(
      value: _warehousesBloc,
      child: Wrapper(
        body: Column(
          children: [
            // Search and filters
            Container(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                children: [
                  // Search field
                  TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    style: Fonts.labelSmall,
                    decoration: InputDecoration(
                      hintText: 'Поиск складов...',
                      prefixIcon: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: SVG(
                          Assets.icons.search,
                          color:
                              isDarkTheme ? AppColors.medium : AppColors.medium,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16.0),

                  // Type filters
                  BlocBuilder<BlocWarehouses, BlocWarehousesState>(
                    builder: (context, state) {
                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _buildTypeFilter(
                              'Все',
                              null,
                              state.selectedType == null,
                              isDarkTheme,
                            ),
                            const SizedBox(width: 8.0),
                            _buildTypeFilter(
                              'Общие',
                              WarehouseType.general,
                              state.selectedType == WarehouseType.general,
                              isDarkTheme,
                            ),
                            const SizedBox(width: 8.0),
                            _buildTypeFilter(
                              'Холодильные',
                              WarehouseType.cold,
                              state.selectedType == WarehouseType.cold,
                              isDarkTheme,
                            ),
                            const SizedBox(width: 8.0),
                            _buildTypeFilter(
                              'Опасные материалы',
                              WarehouseType.hazardous,
                              state.selectedType == WarehouseType.hazardous,
                              isDarkTheme,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // Warehouses grid
            Expanded(
              child: BlocBuilder<BlocWarehouses, BlocWarehousesState>(
                builder: (context, state) {
                  if (state.isLoading) {
                    return _buildLoadingGrid();
                  }

                  if (state.error != null) {
                    return _buildErrorState(state.error!);
                  }

                  if (state.filteredWarehouses.isEmpty) {
                    return _buildEmptyState();
                  }

                  return _buildWarehousesGrid(state.filteredWarehouses);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeFilter(
    String label,
    WarehouseType? type,
    bool isSelected,
    bool isDarkTheme,
  ) {
    return GestureDetector(
      onTap: () {
        _warehousesBloc.add(BlocWarehousesEvents.filterWarehousesByType(type));
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected
              ? AppColors.lightPrimary
              : (isDarkTheme ? AppColors.darkSurface : AppColors.lightSurface),
          borderRadius: BorderRadius.circular(16.0),
          border: Border.all(
            color: isSelected
                ? AppColors.lightPrimary
                : (isDarkTheme ? AppColors.medium : AppColors.medium),
          ),
        ),
        child: Text(
          label,
          style: Fonts.labelSmall.merge(
            TextStyle(
              color: isSelected
                  ? Colors.white
                  : (isDarkTheme
                      ? AppColors.darkPrimary
                      : AppColors.lightPrimary),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: MasonryGridView.count(
        crossAxisCount: _getCrossAxisCount(),
        mainAxisSpacing: 12.0,
        crossAxisSpacing: 12.0,
        itemCount: 6,
        itemBuilder: (context, index) {
          return const WarehouseCard(isLoading: true);
        },
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SVG(
            Assets.icons.help,
            width: 64.0,
            color: Colors.red,
          ),
          const SizedBox(height: 16.0),
          Text(
            'Ошибка загрузки',
            style: Fonts.titleMedium.merge(
              const TextStyle(color: Colors.red),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            error,
            style: Fonts.bodyMedium.merge(
              TextStyle(
                color: isDarkTheme
                    ? AppColors.darkPrimary
                    : AppColors.lightPrimary,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24.0),
          CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () {
              _warehousesBloc.add(const BlocWarehousesEvents.loadWarehouses());
            },
            text: 'Повторить',
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SVG(
            Assets.icons.warehouse,
            width: 64.0,
            color: isDarkTheme ? AppColors.medium : AppColors.medium,
          ),
          const SizedBox(height: 16.0),
          Text(
            'Нет складов',
            style: Fonts.titleMedium.merge(
              TextStyle(
                color: isDarkTheme ? AppColors.medium : AppColors.medium,
              ),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'Создайте первый склад для начала работы',
            style: Fonts.bodyMedium.merge(
              TextStyle(
                color: isDarkTheme ? AppColors.medium : AppColors.medium,
              ),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24.0),
          CustomElevatedButton(
            type: CustomElevatedButtonTypes.accent,
            onPressed: () {
              showBaseDialog(
                context,
                maxWidth: 500,
                builder: (context) => CreateWarehousePopup(
                  onSuccess: () {
                    _warehousesBloc
                        .add(const BlocWarehousesEvents.loadWarehouses());
                  },
                ),
              );
            },
            text: 'Создать склад',
          ),
        ],
      ),
    );
  }

  Widget _buildWarehousesGrid(List<Warehouse> warehouses) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: MasonryGridView.count(
        crossAxisCount: _getCrossAxisCount(),
        mainAxisSpacing: 12.0,
        crossAxisSpacing: 12.0,
        itemCount: warehouses.length,
        itemBuilder: (context, index) {
          final warehouse = warehouses[index];
          return WarehouseCard(
            warehouse: warehouse,
            onTap: () {
              context.router
                  .push(WarehouseContentsRoute(warehouseId: warehouse.id!));
            },
            onEdit: () {
              showBaseDialog(
                context,
                maxWidth: 500,
                builder: (context) => EditWarehousePopup(
                  warehouse: warehouse,
                  onSuccess: () {
                    _warehousesBloc
                        .add(const BlocWarehousesEvents.loadWarehouses());
                  },
                ),
              );
            },
            onDelete: () {
              _showDeleteConfirmation(warehouse);
            },
          );
        },
      ),
    );
  }

  void _showDeleteConfirmation(Warehouse warehouse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Удаление склада'),
        content:
            Text('Вы уверены, что хотите удалить склад "${warehouse.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Отмена'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _warehousesBloc
                  .add(BlocWarehousesEvents.deleteWarehouse(warehouse.id!));
            },
            child: const Text('Удалить'),
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount() {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4;
    if (screenWidth > 800) return 3;
    if (screenWidth > 600) return 2;
    return 1;
  }
}
