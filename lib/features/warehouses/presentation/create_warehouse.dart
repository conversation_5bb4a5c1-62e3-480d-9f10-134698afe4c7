import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

class CreateWarehouseDrawer extends StatefulWidget {
  const CreateWarehouseDrawer({
    super.key,
    this.onSuccess,
  });

  final VoidCallback? onSuccess;

  @override
  State<CreateWarehouseDrawer> createState() => _CreateWarehouseDrawerState();
}

class _CreateWarehouseDrawerState extends State<CreateWarehouseDrawer> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  WarehouseType _selectedType = WarehouseType.general;
  bool _isLoading = false;
  String? _nameError;
  String? _addressError;

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  bool _validateForm() {
    setState(() {
      _nameError = null;
      _addressError = null;
    });

    bool isValid = true;

    if (_nameController.text.trim().isEmpty) {
      setState(() {
        _nameError = 'Название склада обязательно';
      });
      isValid = false;
    } else if (_nameController.text.trim().length < 2) {
      setState(() {
        _nameError = 'Название должно содержать минимум 2 символа';
      });
      isValid = false;
    }

    if (_addressController.text.trim().isEmpty) {
      setState(() {
        _addressError = 'Адрес склада обязателен';
      });
      isValid = false;
    }

    return isValid;
  }

  Future<void> _createWarehouse() async {
    if (!_validateForm()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final warehouseInput = WarehouseCreateInput(
        name: _nameController.text.trim(),
        type: _selectedType,
        address: _addressController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
      );

      final result =
          await PurchaseListRepositoryV2.createWarehouse(warehouseInput);

      if (result.data != null) {
        final logger = Logger();
        logger.d('Warehouse created successfully: ${result.data}');

        CustomDrawer.instance.hide();
        widget.onSuccess?.call();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Склад успешно создан'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(result.data?.message ?? 'Ошибка при создании склада');
      }
    } catch (e) {
      final logger = Logger();
      logger.e('Error creating warehouse: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка при создании склада: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return Column(
      children: [
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(20.0),
            children: [
              Text(
                'Создание склада',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
              const SizedBox(height: 20.0),

              // Name field
              Text(
                'Название склада *',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _nameController,
                // hintText: 'Введите название склада',
                // errorText: _nameError,
                onChanged: (value) {
                  if (_nameError != null) {
                    setState(() {
                      _nameError = null;
                    });
                  }
                },
              ),
              const SizedBox(height: 16.0),

              // Type field
              Text(
                'Тип склада *',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              DropdownButton<WarehouseType>(
                value: _selectedType,
                items: WarehouseType.values.map((type) {
                  return DropdownMenuItem<WarehouseType>(
                    value: type,
                    child: Text(_getTypeLabel(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16.0),

              // Address field
              Text(
                'Адрес склада *',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _addressController,
                // hintText: 'Введите адрес склада',
                // errorText: _addressError,
                maxLines: 2,
                onChanged: (value) {
                  if (_addressError != null) {
                    setState(() {
                      _addressError = null;
                    });
                  }
                },
              ),
              const SizedBox(height: 16.0),

              // Description field
              Text(
                'Описание (необязательно)',
                style: Fonts.bodyMedium.merge(
                  const TextStyle(height: 1.5),
                ),
              ),
              const SizedBox(height: 8.0),
              TextField(
                controller: _descriptionController,
                // hintText: 'Введите описание склада',
                maxLines: 3,
              ),
              const SizedBox(height: 16.0),

              // Type description
              Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: isDarkTheme
                      ? AppColors.darkSurface.withOpacity(0.5)
                      : AppColors.lightSurface.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: isDarkTheme
                        ? AppColors.darkStroke
                        : AppColors.lightStroke,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Информация о типе склада:',
                      style: Fonts.labelMedium.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkPrimary
                              : AppColors.lightPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8.0),
                    Text(
                      _getTypeDescription(_selectedType),
                      style: Fonts.bodySmall.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkDescription
                              : AppColors.lightDescription,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Action buttons
        Container(
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            color: isDarkTheme
                ? AppColors.darkBackground
                : AppColors.lightBackground,
            border: BorderDirectional(
              top: BorderSide(
                color:
                    isDarkTheme ? AppColors.darkStroke : AppColors.lightStroke,
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  onPressed: () {
                    if (!_isLoading) {
                      CustomDrawer.instance.hide();
                    }
                  },
                  text: 'Отмена',
                ),
              ),
              const SizedBox(width: 12.0),
              Expanded(
                child: CustomElevatedButton(
                  type: CustomElevatedButtonTypes.accent,
                  onPressed: () {
                    if (!_isLoading) {
                      _createWarehouse();
                    }
                  },
                  text: _isLoading ? 'Создание...' : 'Создать склад',
                  disabled: _isLoading,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getTypeLabel(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return 'Общий склад';
      case WarehouseType.cold:
        return 'Холодильный склад';
      case WarehouseType.hazardous:
        return 'Склад опасных материалов';
    }
  }

  String _getTypeDescription(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return 'Обычный склад для хранения стандартных материалов и товаров при комнатной температуре.';
      case WarehouseType.cold:
        return 'Склад с контролируемой температурой для хранения материалов, требующих охлаждения.';
      case WarehouseType.hazardous:
        return 'Специализированный склад для хранения опасных материалов с соблюдением особых мер безопасности.';
    }
  }
}
